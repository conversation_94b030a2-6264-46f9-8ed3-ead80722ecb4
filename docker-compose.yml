name: serplens

services:
  payload:
    image: node:20-alpine
    ports:
      - '3000:3000'
    volumes:
      - .:/home/<USER>/app
      - node_modules:/home/<USER>/app/node_modules
    working_dir: /home/<USER>/app/
    command: sh -c "corepack enable && corepack prepare pnpm@9.0.0 --activate && pnpm install && pnpm run dev"
    depends_on:
      - postgres
      # - mongo
    env_file:
      - .env

  # Ensure your DATABASE_URI uses 'postgresql' as the hostname ie. postgresql://127.0.0.1:5432/your-database-name
  postgres:
    restart: always
    image: postgres:latest
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - '54320:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_DB: serplens
      POSTGRES_HOST_AUTH_METHOD: trust
  nginx:
    image: nginx:latest
    ports:
      - 80:80
      - 443:443
    restart: always
    volumes:
      - ./nginx/conf/:/etc/nginx/conf.d/:ro
      - ./nginx/certs:/etc/nginx/ssl
    attach: false
    logging:
      driver: 'none'

volumes:
  pgdata:
  # data:
  node_modules:
