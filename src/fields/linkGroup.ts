import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, Field } from 'payload'
import deepMerge from '@/utils/deepMerge'
import { type LinkAppearances, link } from './link'

type LinkGroupType = (options?: {
  appearances?: LinkAppearances[] | false
  overrides?: Partial<ArrayField>
}) => Field

export const linkGroup: LinkGroupType = ({ appearances, overrides = {} } = {}) => {
  const generatedLinkGroup: Field = {
    name: 'links',
    type: 'array',
    fields: [
      link({
        appearances
      })
    ],
    admin: {
      initCollapsed: true
    }
  }

  return deepMerge(generatedLinkGroup, overrides)
}
