import React from 'react'
import {
  type FieldErrorsImpl,
  type FieldValues,
  type UseFormRegister,
  useFormContext
} from 'react-hook-form'
import { Error } from '@/blocks/form/error'
import { Width } from '@/blocks/form/width'
import { Checkbox as CheckboxUi, Label } from '@/components'
import type { CheckboxField } from '@payloadcms/plugin-form-builder/types'

export const Checkbox: React.FC<
  CheckboxField & {
    errors: Partial<FieldErrorsImpl>
    register: UseFormRegister<FieldValues>
  }
> = ({ name, defaultValue, errors, label, register, required, width }) => {
  const props = register(name, { required: required })
  const { setValue } = useFormContext()

  return (
    <Width width={width}>
      <div className='flex items-center gap-2'>
        <CheckboxUi
          defaultChecked={defaultValue}
          id={name}
          {...props}
          onCheckedChange={(checked) => {
            setValue(props.name, checked)
          }}
        />
        <Label htmlFor={name}>
          {required && (
            <span className='required'>
              * <span className='sr-only'>(required)</span>
            </span>
          )}
          {label}
        </Label>
      </div>
      {errors[name] && <Error name={name} />}
    </Width>
  )
}
