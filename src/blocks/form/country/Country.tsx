import React from 'react'
import { type Control, Controller, type FieldErrorsImpl } from 'react-hook-form'
import { Error } from '@/blocks/form/error'
import { Width } from '@/blocks/form/width'
import {
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components'
import type { CountryField } from '@payloadcms/plugin-form-builder/types'
import { countryOptions } from './options'

export const Country: React.FC<
  CountryField & {
    control: Control
    errors: Partial<FieldErrorsImpl>
  }
> = ({ name, control, errors, label, required, width }) => {
  return (
    <Width width={width}>
      <Label className='' htmlFor={name}>
        {label}

        {required && (
          <span className='required'>
            * <span className='sr-only'>(required)</span>
          </span>
        )}
      </Label>
      <Controller
        control={control}
        defaultValue=''
        name={name}
        render={({ field: { onChange, value } }) => {
          const controlledValue = countryOptions.find((t) => t.value === value)

          return (
            <Select onValueChange={(val) => onChange(val)} value={controlledValue?.value}>
              <SelectTrigger className='w-full' id={name}>
                <SelectValue placeholder={label} />
              </SelectTrigger>
              <SelectContent>
                {countryOptions.map(({ label, value }) => {
                  return (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          )
        }}
        rules={{ required }}
      />
      {errors[name] && <Error name={name} />}
    </Width>
  )
}
