import path from 'path'
import { PayloadRequest, buildConfig } from 'payload'
import sharp from 'sharp'
import { fileURLToPath } from 'url'
import { categories } from '@/collections/categories'
import { media } from '@/collections/media'
import { pages } from '@/collections/pages'
import { posts } from '@/collections/posts'
import { users } from '@/collections/users'
import { defaultLexical } from '@/fields/defaultLexical'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { resendAdapter } from '@payloadcms/email-resend'
import { footer } from './collections/footer'
import { header } from './collections/header'
import { plugins } from './plugins/plugins'
import { getServerSideURL } from './utilities/getURL'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeLogin` statement on line 15.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeDashboard` statement on line 15.
      beforeDashboard: ['@/components/BeforeDashboard']
    },
    importMap: {
      baseDir: path.resolve(dirname)
    },
    user: users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900
        }
      ]
    }
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: defaultLexical,
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.POSTGRES_URL || ''
    }
  }),
  collections: [pages, posts, media, categories, users],
  cors: [getServerSideURL()].filter(Boolean),
  globals: [header, footer],
  plugins: [...plugins],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts')
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      }
    },
    tasks: []
  },
  email: resendAdapter({
    defaultFromAddress: process.env.FROM_ADDRESS || '',
    defaultFromName: process.env.FROM_NAME || '',
    apiKey: process.env.RESEND_API_KEY || ''
  })
})
