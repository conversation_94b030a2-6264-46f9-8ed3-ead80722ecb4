'use client'

import Link from 'next/link'
import {
  <PERSON><PERSON>,
  Header<PERSON>rapper,
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  Text,
  buttonVariants
} from '@/components'
import { cn } from '@/utilities/ui'

export default function Page() {
  return (
    <>
      <HeaderWrapper
        title='Check your email for a code'
        description='Enter the code from your authenticator app.'
        link=''
        linkText=''
      />

      <InputOTP maxLength={6} containerClassName='justify-center bg-primary-foreground'>
        <InputOTPGroup>
          <InputOTPSlot index={0} className='border-border size-12 text-lg' />
          <InputOTPSlot index={1} className='border-border size-12 text-lg' />
          <InputOTPSlot index={2} className='border-border size-12 text-lg' />
        </InputOTPGroup>
        <span className="relative -top-1 px-4 before:content-[''] before:absolute before:left-3 before:bg-primary before:h-2.5 before:w-2.5 before:rounded-full "></span>
        <InputOTPGroup>
          <InputOTPSlot index={3} className='border-border size-12 text-lg' />
          <InputOTPSlot index={4} className='border-border size-12 text-lg' />
          <InputOTPSlot index={5} className='border-border size-12 text-lg' />
        </InputOTPGroup>
      </InputOTP>

      <Text weight='medium' className='text-center mt-5 leading-6'>
        Send code again{' '}
        <Text as='span' weight='medium' className='text-primary leading-6'>
          00:34
        </Text>
      </Text>

      <Button variant='primary' size='lg' className='w-full h-11.5 cursor-pointer mt-5'>
        Continue
      </Button>
      <Link
        href='/signin'
        className={cn(
          buttonVariants({
            variant: 'ghost',
            size: 'lg',
            className: 'w-full h-11.5'
          })
        )}>
        {' '}
        Back to Login
      </Link>
    </>
  )
}
