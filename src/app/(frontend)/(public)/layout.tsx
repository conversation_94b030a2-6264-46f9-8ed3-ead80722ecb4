import { ChevronLeft } from 'lucide-react'
import Link from 'next/link'
import { FormFooter } from '@/components'

export default function layout({
  children
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <>
      <div
        className='absolute inset-0 bg-cover bg-bottom'
        style={{ backgroundImage: "url('/images/bg-image.png')" }}
      />
      <main className='flex flex-col h-screen relative'>
        <div className='absolute top-6 left-6 z-10'>
          <Link
            href='/'
            className='text-white text-sm font-semibold flex gap-2 items-center'>
            <ChevronLeft />
            Home
          </Link>
        </div>
        <div className='flex-grow max-w-md md:max-w-486 w-full mx-auto p-4 flex flex-col justify-center'>
          {children}
        </div>
        <div className='max-w-md md:max-w-486 w-full mx-auto p-4'>
          <FormFooter />
        </div>
      </main>
    </>
  )
}
