'use client'

import { useForm } from 'react-hook-form'
import { z } from 'zod'
import {
  Button,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input
} from '@/components'
import { zodResolver } from '@hookform/resolvers/zod'

const formSchema = z.object({
  email: z.string().nonempty('Email is required').email('Invalid email')
})

export function SignUpForm() {
  const form = useForm({
    resolver: zodResolver(formSchema),

    defaultValues: { email: '' }
  })

  return (
    <Form {...form}>
      <form className='space-y-4'>
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder='<EMAIL>' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          variant='primary'
          size='lg'
          type='submit'
          className='w-full h-11.5 cursor-pointer'>
          Sign Up
        </Button>
      </form>
    </Form>
  )
}
