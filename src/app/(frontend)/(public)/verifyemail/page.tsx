import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  SocialButton,
  Text
} from '@/components'

export default function Page() {
  return (
    <>
      <HeaderWrapper
        title="Verify your email to accept Vip<PERSON>'s invite"
        description='<NAME_EMAIL> for your temprary login code.'
        link=''
        linkText=''
      />
      <div className='space-y-4 md:space-y-6'>
        <SocialButton platform='Google' text='Sign Up with Google' />
        <SocialButton platform='Apple' text='Sign Up with Apple' />
      </div>
      <InputOTP maxLength={6} containerClassName='bg-primary-foreground mt-8'>
        <InputOTPGroup>
          <InputOTPSlot index={0} className='border-border size-12 text-lg' />
          <InputOTPSlot index={1} className='border-border size-12 text-lg' />
          <InputOTPSlot index={2} className='border-border size-12 text-lg' />
        </InputOTPGroup>
        <span className="relative -top-1 px-4 before:content-[''] before:absolute before:left-3 before:bg-primary before:h-2.5 before:w-2.5 before:rounded-full "></span>
        <InputOTPGroup>
          <InputOTPSlot index={3} className='border-border size-12 text-lg' />
          <InputOTPSlot index={4} className='border-border size-12 text-lg' />
          <InputOTPSlot index={5} className='border-border size-12 text-lg' />
        </InputOTPGroup>
      </InputOTP>
      <Text as='p' weight='medium' className='mt-1 leading-6'>
        We sent a code to your inbox
      </Text>
      <Button
        variant='primary'
        size='lg'
        type='submit'
        className='w-full h-11.5 cursor-pointer mt-5'>
        Continue
      </Button>

      <Button
        variant='ghost'
        size='lg'
        type='submit'
        className='w-full h-11.5 cursor-pointer'>
        Resend in 26s
      </Button>
    </>
  )
}
