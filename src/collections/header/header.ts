import type { GlobalConfig } from 'payload'
import { collections } from '@/constants'
import { link } from '@/fields'
import { revalidateHeader } from './hooks'

export const header: GlobalConfig = {
  slug: collections.header.slug,
  access: {
    read: () => true
  },
  fields: [
    {
      name: 'navItems',
      type: 'array',
      fields: [
        link({
          appearances: false
        })
      ],
      maxRows: 6,
      admin: {
        initCollapsed: true,
        components: {
          RowLabel: '@/Header/RowLabel#RowLabel'
        }
      }
    }
  ],
  hooks: {
    afterChange: [revalidateHeader]
  }
}
