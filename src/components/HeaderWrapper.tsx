import Image from 'next/image'
import Link from 'next/link'
import { Text } from '@/components'

export type HeaderProps = {
  title: string
  description: string
  link: string
  linkText: string
}

export function HeaderWrapper({ title, description, link, linkText }: HeaderProps) {
  return (
    <div className='text-center'>
      <Image
        src='/images/icons/logo.svg'
        alt='icons'
        width={130}
        height={130}
        className='h-auto mx-auto mb-3 md:mb-6'
      />
      <Text as='h1' variant='3xl' weight='bold' className='mb-1.5'>
        {title}
      </Text>
      <Text as='p' variant='base' className='mb-3 md:mb-5'>
        {description}
        <Link
          href={link}
          className='text-primary text-base font-bold transition-all hover:underline'>
          {' '}
          {linkText}
        </Link>
      </Text>
    </div>
  )
}
