import React from 'react'
import Image from 'next/image'
import { Button, Text } from '@/components'

export type Platform = 'Google' | 'Apple'

export type SocialButtonProps = {
  platform: Platform
  text: string
  className?: string
}
export function SocialButton({ platform, text }: SocialButtonProps) {
  const platformIcons: Record<Platform, string> = {
    Google: '/images/icons/google.svg',
    Apple: '/images/icons/apple.svg'
  }

  return (
    <Button variant='outline' size='sm' className=''>
      <Image
        src={platformIcons[platform]}
        alt={`${platform} icon`}
        className='size-4'
        height={16}
        width={16}
      />
      <Text as='span' weight='medium'>
        {text}
      </Text>
    </Button>
  )
}
